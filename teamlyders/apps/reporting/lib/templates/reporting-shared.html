<!--
TeamLyders Reporting App - Shared Templates
===========================================

This file contains reusable HTML templates for the reporting application.
Templates use the TeamLyders template system with $[VariableName]$ placeholders.

Usage:
1. Import templates: TL.ImportTemplate("resource?src=templates/reporting-shared.html", callback)
2. Get template: const template = TL.Template("Template-Name")
3. Fill template: const filled = TL.FillTemplate(template, data)

Available Templates:
- Section-Header: Basic section header with optional button
- Content-Section: Complete section with header, description, and content
- Overview-Card: Dashboard overview cards with metrics
- Report-Card: Standard report card with icon, title, description, and actions
- Quick-Action-Card: Dashboard quick action cards
- Category-Item: Sidebar category list items
- Empty-State: Empty state displays with icon and call-to-action
- Content-Header: Page content headers
- Reporting-App-Container: Main app wrapper
- Sidebar: Sidebar navigation container
- Main-Content: Main content area wrapper
- Dashboard-Header: Dashboard page header section
- Grid-Container: Flexible grid container
- Theme-Toggle: Theme toggle button for switching between light and dark mode
-->

<!-- Section Header Template -->
<div class="section-header" data-template="Section-Header">
    <h2>$[Title]$</h2>
    <button class="view-all-btn" data-action="$[Action]$">$[ButtonText]$</button>
</div>

<!-- Section with Description Template -->
<section class="content-section" data-template="Content-Section">
    <div class="section-header">
        <h2>$[Title]$</h2>
        <button class="view-all-btn" data-action="$[Action]$">$[ButtonText]$</button>
    </div>
    <p class="section-description">$[Description]$</p>
    <div class="$[ContentClass]$" id="$[ContentId]$">
        $[Content]$
    </div>
</section>

<!-- Overview Metric Card Template -->
<div class="card overview-card metric-card" data-template="Overview-Card">
    <div class="metric-card-top">
        <div class="metric-title" title="$[Title]$">$[Title]$</div>
    </div>
    <div class="metric-card-main">
        <div class="metric-value">$[Value]$</div>
    </div>
</div>

<!-- Report Card Template -->
<div class="card report-card" data-template="Report-Card">
    <div class="report-card-header">
        <div class="report-icon $[Category]$">
            $[Icon]$
        </div>
        <div class="report-content">
            <h3>$[Title]$</h3>
            <p class="$[DescriptionClass]$">$[Description]$</p>
        </div>
    </div>
    <div class="report-card-footer">
        <button class="generate-btn" data-report="$[Title]$">
            Generate Report
        </button>
        <button class="btn-secondary view-btn" data-action="view" data-report="$[Title]$" aria-label="View $[Title]$">
            View
        </button>
        <button class="favorite-btn $[FavoriteClass]$" title="$[FavoriteTitle]$">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 21l-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
            </svg>
        </button>
    </div>
</div>

<!-- Quick Action Card Template -->
<div class="card quick-action-card" data-template="Quick-Action-Card">
    <div class="quick-action-icon">$[Icon]$</div>
    <div class="quick-action-title">$[Title]$</div>
    <p class="quick-action-description">$[Description]$</p>
</div>

<!-- Category Card Template -->
<div class="card category-card" data-template="Category-Card">
    <div class="category-card-header">
        <div class="category-icon $[CategoryKey]$">$[Icon]$</div>
        <h3 class="category-title">$[Title]$</h3>
    </div>
    <p class="category-description">$[Description]$</p>
    <div class="category-meta">
        <span class="category-report-count">$[ReportCount]$ Reports</span>
        <button class="btn-primary browse-category-btn" data-category="$[CategoryKey]$">View</button>
    </div>
</div>

<!-- Category Item Template -->
<li class="category-item $[ActiveClass]$" data-category="$[Category]$" data-template="Category-Item">
    <div class="category-name">$[Name]$</div>
    <span class="category-count">$[Count]$ Reports</span>
</li>

<!-- Empty State Template -->
<div class="empty-state" data-template="Empty-State">
    <div class="empty-icon">$[Icon]$</div>
    <h3>$[Title]$</h3>
    <p>$[Description]$</p>
    <button class="btn-primary" data-action="$[Action]$">$[ButtonText]$</button>
</div>

<!-- Content Header Template -->
<div class="content-header" data-template="Content-Header">
    <h2>$[Title]$</h2>
    <p>$[Description]$</p>
</div>

<!-- Main App Container Template -->
<div class="reporting-app" data-template="Reporting-App-Container">
    $[Content]$
</div>

<!-- Sidebar Template -->
<aside class="sidebar" data-template="Sidebar">
    <div class="sidebar-header">
        <h1>$[Title]$</h1>
    </div>
    <ul class="category-list" id="$[ListId]$">
        $[Categories]$
    </ul>
</aside>

<!-- Main Content Template -->
<main class="main-content" data-template="Main-Content">
    $[Content]$
</main>

<!-- Dashboard Header Template -->
<section class="dashboard-header" data-template="Dashboard-Header">
    <div class="header-content">
        <h1>$[Title]$</h1>
        <p>$[Description]$</p>
    </div>
</section>

<!-- Grid Container Template -->
<div class="$[GridClass]$" data-template="Grid-Container">
    $[Content]$
</div>

<!-- Single Report View Template (Redesigned) -->
<section class="report-view" data-template="Report-View">
    <!-- Sticky Lightweight Header -->
    <header class="report-view-header" data-role="sticky-header">
        <div class="report-view-bar">
            <div class="rv-left">
                <button class="back-btn btn-secondary" data-action="back" aria-label="Back to reports">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <polyline points="15 18 9 12 15 6" />
                    </svg>
                    <span class="back-label">Back</span>
                </button>
            </div>
            <div class="rv-actions">
                <button class="favorite-btn $[FavoriteClass]$" title="$[FavoriteTitle]$" aria-label="$[FavoriteTitle]$" aria-pressed="false">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21l-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                    </svg>
                </button>
                <button class="generate-btn" data-report="$[InternalName]$" aria-label="Export $[Title]$ Report">Generate Report</button>
            </div>
        </div>
        <div class="report-view-meta">
            <!-- Reuse standard report card icon styling for visual consistency -->
            <div class="report-icon $[Category]$ single-view-icon" aria-hidden="true">$[Icon]$</div>
            <div class="rv-titles">
                <h1 class="rv-title">$[Title]$</h1>
                <p class="rv-subtitle">$[Description]$</p>
            </div>
        </div>
    </header>

    <!-- Content Layout -->
    <div class="report-view-body">
        <!-- Data Table -->
        <div class="report-data-area">
            <div class="report-table-wrapper" id="reportTableContainer">
                <div class="report-toolbar" role="region" aria-label="Search and filters">
                    <div class="rt-left">
                        <div class="rt-search" role="search">
                            <svg class="rt-search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                            <input type="search" class="rt-search-input" placeholder="Search in results" aria-label="Search in results">
                        </div>
                        <div class="rt-filters" aria-label="Quick filters">
                            <button class="btn-secondary rt-filter-btn" data-role="filter" aria-expanded="false">
                                <svg class="rt-filter-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                                    <polygon points="22 3 2 3 10 12 10 19 14 21 14 12 22 3"></polygon>
                                </svg>
                                Filters
                            </button>
                        </div>
                    </div>
                    <div class="rt-right">

                        <div class="rt-select">
                            <label class="rt-select-label" for="rtSortBy">Sort</label>
                            <select id="rtSortBy" class="rt-select-input" aria-label="Sort by">
                                <option value="relevance">Relevance</option>
                                <option value="date_desc">Date (newest)</option>
                                <option value="date_asc">Date (oldest)</option>
                                <option value="value_desc">Value (high to low)</option>
                            </select>
                        </div>

                    </div>
                </div>
                <div class="rt-filter-panel" id="reportFilterPanel" hidden>
                    <form class="rt-filter-form" novalidate>

                        <div class="rt-filter-builder" id="rtFilterBuilder">
                            <div class="rt-filter-row rt-filter-builder-row">
                                <div class="rt-select">
                                    <label class="rt-select-label" for="rtFieldSelect">Field</label>
                                    <select id="rtFieldSelect" class="rt-select-input" aria-label="Field"></select>
                                </div>
                                <div class="rt-select">
                                    <label class="rt-select-label" for="rtOperatorSelect">Operator</label>
                                    <select id="rtOperatorSelect" class="rt-select-input" aria-label="Operator"></select>
                                </div>
                                <div class="rt-value" id="rtValueInputWrap"></div>
                                <button type="button" class="btn-secondary rt-add-filter">Add Filter</button>
                            </div>
                            <div class="rt-active-filters" id="rtActiveFilters" aria-live="polite"></div>
                        </div>
                        <div class="rt-filter-dynamic" id="rtFilterDynamic" hidden></div>
                        <div class="rt-filter-actions">
                            <button type="button" class="btn-secondary rt-filter-reset">Reset Filters</button>
                        </div>
                    </form>
                </div>

                <div class="report-table-container">
                    $[Table]$
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Simple Table Template -->
<div class="report-table-scroller" data-template="Report-Table">
    <table class="report-data-table">
        <thead>
            <tr>$[HeaderCells]$</tr>
        </thead>
        <tbody>
            $[Rows]$
        </tbody>
    </table>
</div>

<!-- Table Header Cell Template -->
<th class="report-header-cell" data-template="Report-Header-Cell">$[Value]$</th>

<!-- Table Row Template -->
<tr class="report-row" data-template="Report-Row">$[Cells]$</tr>

<!-- Table Cell Template -->
<td class="report-cell" data-template="Report-Cell">$[Value]$</td>